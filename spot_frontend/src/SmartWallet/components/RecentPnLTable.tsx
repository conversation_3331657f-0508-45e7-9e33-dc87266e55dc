import React, { useState, useEffect } from 'react';
import { FaSpinner } from 'react-icons/fa';
import { RecentPnLData } from '../SmartWalletDetail';

interface RecentPnLTableProps {
  walletAddress: string;
  tableHeight: number;
}

const RecentPnLTable: React.FC<RecentPnLTableProps> = ({ walletAddress, tableHeight }) => {
  const [data, setData] = useState<RecentPnLData[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const formatNumber = (num: number, decimals: number = 2): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toFixed(decimals);
  };

  const formatPnL = (value: number): { text: string; color: string } => {
    const isPositive = value >= 0;
    const formattedValue = `${isPositive ? '+' : ''}$${formatNumber(Math.abs(value))}`;
    return {
      text: formattedValue,
      color: isPositive ? 'text-green-400' : 'text-red-400'
    };
  };

  // Mock data for demonstration
  const mockData: RecentPnLData[] = [
    {
      id: '1',
      tokenSymbol: 'bop',
      tokenName: 'bop',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '1h',
      unrealized: 324.48,
      realizedProfit: 324.48,
      totalProfit: 324.48,
      balance: 0,
      position: 0,
      bought: 594.25,
      sold: 918.73,
      transactions30d: 2
    },
    {
      id: '2',
      tokenSymbol: 'PEPE',
      tokenName: 'PEPE',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '2h',
      unrealized: -38.98,
      realizedProfit: -38.98,
      totalProfit: -38.98,
      balance: 0,
      position: 0,
      bought: 582.25,
      sold: 793.26,
      transactions30d: 1
    },
    {
      id: '3',
      tokenSymbol: 'Doxy PEP',
      tokenName: 'Doxy PEP',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '3h',
      unrealized: 3158.8,
      realizedProfit: 3158.8,
      totalProfit: 3158.8,
      balance: 0,
      position: 0,
      bought: 514.72,
      sold: 262.83,
      transactions30d: 2
    },
    {
      id: '4',
      tokenSymbol: 'TRUMP',
      tokenName: 'TRUMP',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '4h',
      unrealized: -117.25,
      realizedProfit: -117.25,
      totalProfit: -117.25,
      balance: 0,
      position: 0,
      bought: 595.39,
      sold: 478.14,
      transactions30d: 2
    },
    {
      id: '5',
      tokenSymbol: 'BONK',
      tokenName: 'BONK',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '5h',
      unrealized: -7.6,
      realizedProfit: -7.6,
      totalProfit: -7.6,
      balance: 0,
      position: 0,
      bought: 592.99,
      sold: 588.39,
      transactions30d: 3
    },
    {
      id: '6',
      tokenSymbol: 'WIF',
      tokenName: 'dogwifhat',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '6h',
      unrealized: 1245.67,
      realizedProfit: 1245.67,
      totalProfit: 1245.67,
      balance: 125.5,
      position: 15.2,
      bought: 850.33,
      sold: 2096.0,
      transactions30d: 8
    },
    {
      id: '7',
      tokenSymbol: 'POPCAT',
      tokenName: 'Popcat',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '8h',
      unrealized: -89.45,
      realizedProfit: -89.45,
      totalProfit: -89.45,
      balance: 0,
      position: 0,
      bought: 234.56,
      sold: 145.11,
      transactions30d: 1
    },
    {
      id: '8',
      tokenSymbol: 'MOODENG',
      tokenName: 'Moo Deng',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '12h',
      unrealized: 567.89,
      realizedProfit: 567.89,
      totalProfit: 567.89,
      balance: 45.2,
      position: 8.7,
      bought: 432.11,
      sold: 1000.0,
      transactions30d: 4
    },
    {
      id: '9',
      tokenSymbol: 'GOAT',
      tokenName: 'Goatseus Maximus',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '1d',
      unrealized: 2134.56,
      realizedProfit: 2134.56,
      totalProfit: 2134.56,
      balance: 89.3,
      position: 22.1,
      bought: 1200.0,
      sold: 3334.56,
      transactions30d: 12
    },
    {
      id: '10',
      tokenSymbol: 'PNUT',
      tokenName: 'Peanut the Squirrel',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '1d',
      unrealized: -156.78,
      realizedProfit: -156.78,
      totalProfit: -156.78,
      balance: 0,
      position: 0,
      bought: 678.90,
      sold: 522.12,
      transactions30d: 3
    },
    {
      id: '11',
      tokenSymbol: 'FWOG',
      tokenName: 'FWOG',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '2d',
      unrealized: 789.12,
      realizedProfit: 789.12,
      totalProfit: 789.12,
      balance: 67.8,
      position: 12.4,
      bought: 345.67,
      sold: 1134.79,
      transactions30d: 6
    },
    {
      id: '12',
      tokenSymbol: 'CHILLGUY',
      tokenName: 'Just a chill guy',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '2d',
      unrealized: -234.56,
      realizedProfit: -234.56,
      totalProfit: -234.56,
      balance: 0,
      position: 0,
      bought: 567.89,
      sold: 333.33,
      transactions30d: 2
    },
    {
      id: '13',
      tokenSymbol: 'PONKE',
      tokenName: 'Ponke',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '3d',
      unrealized: 1456.78,
      realizedProfit: 1456.78,
      totalProfit: 1456.78,
      balance: 234.5,
      position: 18.9,
      bought: 890.12,
      sold: 2346.90,
      transactions30d: 9
    },
    {
      id: '14',
      tokenSymbol: 'RETARDIO',
      tokenName: 'Retardio',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '3d',
      unrealized: -67.89,
      realizedProfit: -67.89,
      totalProfit: -67.89,
      balance: 0,
      position: 0,
      bought: 123.45,
      sold: 55.56,
      transactions30d: 1
    },
    {
      id: '15',
      tokenSymbol: 'GIGA',
      tokenName: 'GIGA',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '4d',
      unrealized: 345.67,
      realizedProfit: 345.67,
      totalProfit: 345.67,
      balance: 78.9,
      position: 9.8,
      bought: 456.78,
      sold: 802.45,
      transactions30d: 5
    },
    {
      id: '16',
      tokenSymbol: 'MICHI',
      tokenName: 'Michi',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '5d',
      unrealized: 2345.67,
      realizedProfit: 2345.67,
      totalProfit: 2345.67,
      balance: 156.7,
      position: 25.3,
      bought: 1234.56,
      sold: 3580.23,
      transactions30d: 15
    },
    {
      id: '17',
      tokenSymbol: 'SLERF',
      tokenName: 'Slerf',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '6d',
      unrealized: -123.45,
      realizedProfit: -123.45,
      totalProfit: -123.45,
      balance: 0,
      position: 0,
      bought: 345.67,
      sold: 222.22,
      transactions30d: 2
    },
    {
      id: '18',
      tokenSymbol: 'MYRO',
      tokenName: 'Myro',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '1w',
      unrealized: 678.90,
      realizedProfit: 678.90,
      totalProfit: 678.90,
      balance: 89.4,
      position: 11.2,
      bought: 567.89,
      sold: 1246.79,
      transactions30d: 7
    },
    {
      id: '19',
      tokenSymbol: 'BOME',
      tokenName: 'BOOK OF MEME',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '1w',
      unrealized: -456.78,
      realizedProfit: -456.78,
      totalProfit: -456.78,
      balance: 0,
      position: 0,
      bought: 789.12,
      sold: 332.34,
      transactions30d: 3
    },
    {
      id: '20',
      tokenSymbol: 'MEW',
      tokenName: 'cat in a dogs world',
      tokenImage: '/api/placeholder/24/24',
      lastActive: '1w',
      unrealized: 1234.56,
      realizedProfit: 1234.56,
      totalProfit: 1234.56,
      balance: 123.4,
      position: 16.7,
      bought: 678.90,
      sold: 1913.46,
      transactions30d: 11
    }
  ];

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        setData(mockData);
      } catch (error) {
        console.error('Error fetching recent PnL data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [walletAddress]);

  if (isLoading) {
    return (
      <div
        className="flex items-center justify-center"
        style={{ height: `${tableHeight}px` }}
      >
        <div className="flex items-center">
          <FaSpinner className="animate-spin text-[#7FFFD4] text-2xl mr-3" />
          <span className="text-white text-lg">Loading recent PnL data...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Fixed Header */}
      <div className="bg-[#1A1E24] border-b border-gray-700/50 px-4 sm:px-6 py-4">
        <div className="grid grid-cols-4 sm:grid-cols-5 md:grid-cols-8 lg:grid-cols-10 gap-1 sm:gap-2 text-xs font-medium text-gray-400 uppercase tracking-wider">
          <div className="col-span-1">Token</div>
          <div className="text-center">Last Active</div>
          <div className="text-right hidden sm:block">Unrealized</div>
          <div className="text-right">Realized Profit</div>
          <div className="text-right">Total Profit</div>
          <div className="text-right hidden md:block">Balance</div>
          <div className="text-right hidden lg:block">Position</div>
          <div className="text-right hidden md:block">Bought</div>
          <div className="text-right hidden md:block">Sold</div>
          <div className="text-right">30D TXs</div>
        </div>
      </div>

      {/* Scrollable Body */}
      <div
        className="overflow-y-auto custom-scrollbar"
        style={{ maxHeight: `${tableHeight - 60}px` }}
      >
        {data.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="text-gray-400 text-lg mb-2">No recent PnL data</div>
              <div className="text-gray-500 text-sm">No trading activity found for this wallet</div>
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-800/50">
            {data.map((item) => {
              const unrealized = formatPnL(item.unrealized);
              const realizedProfit = formatPnL(item.realizedProfit);
              const totalProfit = formatPnL(item.totalProfit);

              return (
                <div key={item.id} className="px-4 sm:px-6 py-4 hover:bg-[#1F2329] transition-colors">
                  <div className="grid grid-cols-4 sm:grid-cols-5 md:grid-cols-8 lg:grid-cols-10 gap-1 sm:gap-2 items-center">
                    {/* Token */}
                    <div className="col-span-1 flex items-center space-x-2">
                      <div className="w-6 h-6 bg-gradient-to-br from-[#7FFFD4] to-[#025FDA] rounded-full flex items-center justify-center flex-shrink-0">
                        <span className="text-black text-xs font-bold">
                          {item.tokenSymbol.slice(0, 2)}
                        </span>
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="text-white font-medium text-sm truncate">{item.tokenSymbol}</div>
                      </div>
                    </div>

                    {/* Last Active */}
                    <div className="text-center text-gray-400 text-xs">
                      {item.lastActive}
                    </div>

                    {/* Unrealized */}
                    <div className={`text-right font-medium text-sm ${unrealized.color} hidden sm:block`}>
                      {unrealized.text}
                    </div>

                    {/* Realized Profit */}
                    <div className={`text-right font-medium text-sm ${realizedProfit.color}`}>
                      {realizedProfit.text}
                    </div>

                    {/* Total Profit */}
                    <div className={`text-right font-medium text-sm ${totalProfit.color}`}>
                      {totalProfit.text}
                    </div>

                    {/* Balance */}
                    <div className="text-right text-white text-sm hidden md:block">
                      ${formatNumber(item.balance)}
                    </div>

                    {/* Position */}
                    <div className="text-right text-white text-sm hidden lg:block">
                      {item.position}%
                    </div>

                    {/* Bought */}
                    <div className="text-right text-white text-sm hidden md:block">
                      ${formatNumber(item.bought)}
                    </div>

                    {/* Sold */}
                    <div className="text-right text-white text-sm hidden md:block">
                      ${formatNumber(item.sold)}
                    </div>

                    {/* 30D TXs */}
                    <div className="text-right text-gray-400 text-sm">
                      {item.transactions30d}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default RecentPnLTable;
